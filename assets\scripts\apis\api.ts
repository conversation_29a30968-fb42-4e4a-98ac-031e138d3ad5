// 请求
import { doGet, doPost, postJSON } from '../utils/request';
export default class Request {
  /**
   * 查询当前会话是否有效
   */
  public static checkSession() {
    return doGet('/Auth/v3/user/session/status').then((res: any) => {
      const ok = res.status === true && res.anonymous !== true;
      return { ok };
    });
  }

  public static getUserInfo() {
    return doGet('/Auth/v3/user/info').then((res: any) => {
      return res;
    });
  }
  // 抽奖
  public static getLottery() {
    return doPost('/Radio/emergency/draw', { actionId: 2 }).then((res: any) => {
      return res;
    });
  }
  // 更新关卡进度
  public static updatePassLevelData(data: any) {
    return postJSON('/Radio/emergency/progress/update', {
      ...data,
      actionId: 2,
    }).then((res: any) => {
      return res;
    });
  }
  // 获取关卡进度
  public static getPassLevelData() {
    return doGet('/Radio/emergency/get/progress', { actionId: 2 }).then(
      (res: any) => {
        return res;
      }
    );
  }
  // 获取排行榜
  public static getRankList(data: any) {
    return doGet('/Radio/emergency/leaderboard', { ...data, actionId: 2 }).then(
      (res: any) => {
        return res;
      }
    );
  }
}
