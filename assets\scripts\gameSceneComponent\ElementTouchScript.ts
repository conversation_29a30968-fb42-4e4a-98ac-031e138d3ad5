// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
@ccclass
export default class NewClass extends cc.Component {
  // @property(cc.Node)
  // maskMessage: cc.Node = null;

  // @property(cc.Node)
  // maskSuccess: cc.Node = null;

  currentLevelKnowledgeData =
    globalVariables.LevelKnowledgeData[globalVariables.currentLevel - 1];

  onLoad() {
    this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }

  onTouchEnd(event) {
    let maskMessage = cc.find('Canvas/maskMessage');

    // console.log('been touch1');
    // 获取当前节点在父节点中的index
    let index = this.node['elementIndex']; // 获取同级索引
    if (globalVariables.currentFoundElementIndexArray[index] !== 1) {
      globalVariables.currentFoundElement++;
    }
    globalVariables.currentFoundElementIndexArray[index] = 1;
    // 显示当前隐患提示
    let explainBg = this.node.getChildByName('explainBg');

    if (explainBg && !explainBg.active) {
      explainBg.active = true;
      // // 先将explainBg的透明度设置为0
      // explainBg.opacity = 0;
      let textLabel = explainBg.getChildByName('explainText');
      let position = this.currentLevelKnowledgeData[index].tipsPosition;
      explainBg.width = textLabel.width + 40;
      explainBg.height = textLabel.height + 60;
      explainBg.x = position ? position[0] : 0;
      explainBg.y = position ? position[1] : 0;
      cc.tween(explainBg).by(0.5, { x: 110, opacity: 255 }).start();
      // }, 500);
    }

    maskMessage.getComponent('maskMessageManager').knowledgeIndex = index;
    setTimeout(() => {
      // 设置maskMessage的index属性
      // maskMessage.active = true;
      // 不展示maskMessage了，直接展示maskSuccess
      this.isFindAll();
    }, 500);
    // 移除当前节点的触摸结束事件监听器
    // this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
  onDestroy() {
    cc.systemEvent.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
  }
  // 判断是否找完
  isFindAll() {
    let shouldFoundElement =
      globalVariables.LevelShouldFoundElementArray[
        globalVariables.currentLevel - 1
      ];
    if (globalVariables.currentFoundElement == shouldFoundElement) {
      let maskSuccess = cc.find('Canvas/maskSuccess');
      // 如果通关了，则更换handleBtn的图片为lottery
      if (
        globalVariables.currentLevel == 4 ||
        (globalVariables.passLevelArray[3] == 1 &&
          globalVariables.passTimeArray[3] != -1 &&
          globalVariables.passTimeArray[3] != 0)
      ) {
        cc.resources.load(
          'buttons/lottery',
          cc.SpriteFrame,
          (err, spriteFrame) => {
            if (err) {
              cc.error('加载图片失败: ' + err);
              return;
            }
            // 使用 spriteFrame
            maskSuccess
              .getChildByName('handleBtn')
              .getComponent(cc.Sprite).spriteFrame =
              spriteFrame as cc.SpriteFrame;
            maskSuccess.active = true;
            // this.showKindTips();
          }
        );
        return;
      }
      // setTimeout(() => {
      maskSuccess.active = true;
      // this.showKindTips();
      // }, 1100);
    }
  }
}
