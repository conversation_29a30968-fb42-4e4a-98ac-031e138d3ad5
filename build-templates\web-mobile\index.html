<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />

    <title>“慧眼寻安”找茬挑战小游戏</title>

    <!--http://www.html5rocks.com/en/mobile/mobifying/-->
    <meta
      name="viewport"
      content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1" />

    <!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />

    <!-- force webkit on 360 -->
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <!-- force edge on IE -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- force full screen on some browser -->
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />

    <!-- force screen orientation on some browser -->
    <meta name="screen-orientation" content="" />
    <meta name="x5-orientation" content="" />

    <!--fix fireball/issues/3568 -->
    <!--<meta name="browsermode" content="application">-->
    <meta name="x5-page-mode" content="app" />

    <!--<link rel="apple-touch-icon" href=".png" />-->
    <!--<link rel="apple-touch-icon-precomposed" href=".png" />-->
    <link rel="stylesheet" href="./libs/xgplayer/index.min.css" />

    <link rel="stylesheet" type="text/css" href="./style-mobile.d09dc.css" />
    <link rel="icon" href="favicon.8de18.ico" />
    <script>
      if (/MicroMessenger/i.test(navigator.userAgent))
        document.write(
          '<script src="/actions/static/js/libs/weixn/jweixin-1.6.0.js?v=2019"><\/script>'
        );
    </script>
  </head>
  <body>
    <canvas
      id="GameCanvas"
      oncontextmenu="event.preventDefault()"
      tabindex="0"></canvas>
    <!-- <div id="splash">
    <div class="progress-bar stripes">
      <span style="width: 0%"></span>
    </div>
  </div> -->
    <div id="preload">
      <div class="preload-content">
        <div class="loader">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <div id="progress">1%</div>
      </div>
    </div>
    <script src="./src/settings.df17e.js" charset="utf-8"></script>
    <script src="./libs/xgplayer/index.min.js" charset="utf-8"></script>

    <!-- <script src="vconsole.min.bac6b.js"></script> -->
    <script src="./main.972c6.js" charset="utf-8"></script>

    <script type="text/javascript">
      (function () {
        // open web debugger console
        if (typeof VConsole !== 'undefined') {
          window.vConsole = new VConsole();
        }

        var debug = window._CCSettings.debug;
        // var splash = document.getElementById('splash');
        // splash.style.display = 'block';
        var preload = document.getElementById('preload');
        preload.style.display = 'block';

        function loadScript(moduleName, cb) {
          function scriptLoaded() {
            document.body.removeChild(domScript);
            domScript.removeEventListener('load', scriptLoaded, false);
            cb && cb();
          }
          var domScript = document.createElement('script');
          domScript.async = true;
          domScript.src = moduleName;
          domScript.addEventListener('load', scriptLoaded, false);
          document.body.appendChild(domScript);
        }

        loadScript(
          debug ? 'cocos2d-js.js' : 'cocos2d-js-min.5f13a.js',
          function () {
            if (CC_PHYSICS_BUILTIN || CC_PHYSICS_CANNON) {
              loadScript(
                debug ? 'physics.js' : 'physics-min.7f489.js',
                window.boot
              );
            } else {
              window.boot();
            }
          }
        );
      })();
    </script>
  </body>
</html>
