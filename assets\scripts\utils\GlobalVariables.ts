// 全局变量

import { getImageURL } from './generalUtil';
class GlobalVariables {
  public loggedIn: boolean = false;
  public userInfo: any = null;
  public userUUID: string = ''; // 用户唯一标识UUID
  public shareInfo: any = {
    title: '“慧眼寻安”找茬挑战小游戏',
    desc: '安全隐患藏不住“慧眼寻安”找茬挑战，第3关我就卡住了…你能过几关？',
    imgUrl: getImageURL('FibsI8ITZipShQkQ-Ywpva-The4e'),
    link: `https://${
      CC_DEBUG ? 'dev' : 'radio'
    }.jgrm.net/actions/game/highway/index.html`,
  };
  public showMarkBlack: boolean = true;
  public hasShook: boolean = false; // 是否摇了签筒
  public fortuneSticksNum: number = null; // 抽中的幸运签
  public currentLevel: number = 1;
  public currentFoundElement: number = 0;
  public currentFoundElementIndexArray: Array<number> = [
    0, 0, 0, 0, 0, 0, 0, 0,
  ];
  public isFirstLoadGame: boolean = true;
  public ifInitCloudData: boolean = false;
  public LevelShouldFoundElementArray: Array<number> = [3, 5, 8, 7];
  public passLevelArray: Array<number> = [1, 0, 0, 0];
  public passTimeArray: Array<number> = [-1, -1, -1, -1];
  public openId: string = '';
  public drawsNum: number = 3;
  public awardsArray: Array<number> = [0, 0, 0, 0, 0];
  public checkInArray: Array<number> = [
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0,
  ];
  public LevelElementData: Array<
    Array<{
      resourcesURL: string;
      position: [number, number];
      size?: [number, number];
      explainPosition?: [number, number];
    }>
  > = [
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_car',
        position: [-21, -1768],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_car1',
        position: [-94, -3848],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_xingren',
        position: [40, -4976],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_hulan',
        position: [164, -478],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_zw',
        position: [-155, -1133],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_zsp',
        position: [186, -1900],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_car',
        position: [15, -3204],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_deng',
        position: [147, -4409],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_jk',
        position: [117, -300],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_ssf',
        position: [-59, -811],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_xsp',
        position: [87, -1326],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_hl',
        position: [62, -2601],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_sg',
        position: [-95, -3397],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_aqck',
        position: [67, -4422],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_pdx',
        position: [37, -5351],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_mhq',
        position: [-130, -5502],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_zfd',
        position: [36, -1511],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_ggp',
        position: [128, -2265],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_lmbj',
        position: [-80, -2554],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_lbggp',
        position: [164, -3278],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_mb',
        position: [-140, -3646],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_lf',
        position: [-67, -4450],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/h4_ss',
        position: [5, -5544],
      },
    ],
  ];
  // 添加LevelKnowledgeData类型定义
  public LevelKnowledgeData: Array<
    Array<{
      content: string;
      tips: string;
      tipsPosition?: [number, number];
    }>
  > = [
    [
      {
        content: '',
        tips: '货物掉落未及时清理',
      },
      {
        content: '',
        tips: '未按规定放置警示标志的故障车辆',
      },
      {
        content: '',
        tips: '行人违规进入高速公路',
      },
    ],
    [
      {
        content: '',
        tips: '高速公路防护栏破损未修复',
        tipsPosition: [-41, 0],
      },
      {
        content: '',
        tips: '路面出现坑洼未填补',
      },
      {
        content: '',
        tips: '交通标志牌被遮挡',
        tipsPosition: [-41, 0],
      },
      {
        content: '',
        tips: '车辆违规占用应急车道',
      },
      {
        content: '',
        tips: '隧道内照明故障',
        tipsPosition: [-41, 0],
      },
      {
        content: '',
        tips: '消防栓破损漏水安全隐患',
        // tipsPosition: [-157, 34],
      },
    ],

    [
      {
        content: '',
        tips: '道路监控控制箱门遗失',
      },
      {
        content: '',
        tips: '伸缩缝有杂物',
      },
      {
        content: '',
        tips: '危险品车辆限速标识倒伏',
      },
      {
        content: '',
        tips: '波形护拦板断裂',
        // tipsPosition: [90, -47],
      },
      {
        content: '',
        tips: '施工区域警示设置不规范',
        // tipsPosition: [-47, -53],
      },
      {
        content: '',
        tips: '安全出口指示灯故障',
      },
      {
        content: '',
        tips: '配电箱有积水渗入',
        // tipsPosition: [-47, -57],
      },
      {
        content: '',
        tips: '灭火器压力不足或失效',
        // tipsPosition: [-47, -57],
      },
    ],
    [
      {
        content: '',
        tips: '中分带竖版断裂',
      },
      {
        content: '',
        tips: '桥上广告牌破损掉落',
      },
      {
        content: '',
        tips: '立面标记破损',
      },
      {
        content: '',
        tips: '路边广告牌陈日破损',
        // tipsPosition: [-60, -177],
      },
      {
        content: '',
        tips: '路中间木板掉落',
      },
      {
        content: '',
        tips: '路面有裂缝',
        // tipsPosition: [-47, -57],
      },
      {
        content: '',
        tips: '山体滑坡未及时清理',
      },
    ],
  ];
}
export let globalVariables: GlobalVariables = new GlobalVariables();
