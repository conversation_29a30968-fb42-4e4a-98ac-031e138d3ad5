/**
 * 设备检测工具
 */
export class DeviceDetector {
  /**
   * 判断是否为平板设备
   */
  static isTablet(): boolean {
    const { width, height } = cc.view.getVisibleSize();
    const minSize = Math.min(width, height);
    const maxSize = Math.max(width, height);
    const aspectRatio = maxSize / minSize;
    
    // 平板判断条件：
    // 1. 屏幕最小边 >= 768px (iPad mini标准)
    // 2. 宽高比 < 1.8 (区别于长条形手机)
    return minSize >= 768 && aspectRatio < 1.8;
  }
  
  /**
   * 获取设备类型
   */
  static getDeviceType(): 'phone' | 'tablet' | 'desktop' {
    if (cc.sys.platform === cc.sys.DESKTOP_BROWSER) {
      return 'desktop';
    }
    return this.isTablet() ? 'tablet' : 'phone';
  }
}