// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class DrawTubeManager extends cc.Component {
  @property(cc.Node)
  drawTube: cc.Node = null;

  @property(cc.Node)
  drawTubeGif: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  fail: cc.Node = null;

  @property(cc.Node)
  win: cc.Node = null;

  @property(cc.Label)
  prizeText: cc.Label = null;

  @property(cc.Node)
  acceptBtn: cc.Node = null;

  duration: number = 0.8;

  // 添加自定义缩放变量，可以被外部组件访问和修改
  public customScale: number = 1;

  // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
  // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
  onLoad() {
    // this.node.scale = 0.7;
    // this.node.opacity = 100;
    // if (this.lotteryInfo?.json?.prizeType) {
    //   this.showAward();
    //   this.goldNumText.string = this.lotteryInfo.json.msg.toString();
    //   this.showGold(); // 暂不区分金币和优惠券，统一用金币的样式展示
    // } else {
    //   this.showTube();
    // }
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  againBtnClick() {
    this.backToLevelSelect();
  }

  acceptBtnClick() {
    this.drawTube.active = true;
    this.maskLottery.active = false;
    this.win.active = false;
    this.fail.active = false;
  }

  showTube() {
    // 显示抽签结果，隐藏中奖
    this.drawTubeGif.active = false;
    this.fail.active = true;
  }
  // 显示抽过的签
  showRestoreTube() {
    this.maskLottery.active = true;
    this.win.active = false;
    this.fail.active = true;
  }

  showAward() {
    if (globalVariables.hasShook) {
      // 显示缓存的签
      this.showRestoreTube();
      return;
    }
    this.maskLottery.active = true;
    this.drawTubeGif.active = true;
    this.drawTubeGif.anchorX = 0;
    this.drawTubeGif.anchorY = 0.5;

    // 优化后的左右摇摆动画，更加自然流畅
    this.playDrawTubeShakeAnimation().then(() => {
      // 动画完成后显示中奖结果
      // this.drawTube.active = false;
      // 在本地存储一个标志位，表示已经摇了
      globalVariables.hasShook = true;
      this.showTube();
    });
  }

  playDrawTubeShakeAnimation(): Promise<void> {
    return new Promise(resolve => {
      // 设置签筒锚点为底部中间 (0, 0.5)
      this.drawTubeGif.anchorX = 0;
      this.drawTubeGif.anchorY = 0.5;

      // 记录初始角度（已旋转180度）
      const initialAngle = this.drawTubeGif.angle;

      // 使用优化后的自然摇摆动画
      // 创建更自然的摇摆动画序列
      // 随机化摇摆参数，增加自然感
      const leftAngle = initialAngle - (12 + Math.random() * 6); // 12-18度随机
      const rightAngle = initialAngle + (12 + Math.random() * 6); // 12-18度随机
      // 时间也稍微随机化，避免过于机械
      const leftTime = 0.3 + Math.random() * 0.2; // 0.3-0.5秒
      const rightTime = 0.4 + Math.random() * 0.3; // 0.4-0.7秒
      const centerTime = 0.25 + Math.random() * 0.15; // 0.25-0.4秒

      const createShakeSequence = () => {
        return cc
          .tween()
          .to(leftTime, { angle: leftAngle }, { easing: 'quadOut' })
          .to(rightTime, { angle: rightAngle }, { easing: 'sineInOut' })
          .to(centerTime, { angle: initialAngle }, { easing: 'quadIn' });
      };

      // 启动循环动画
      cc.tween(this.drawTubeGif).repeatForever(createShakeSequence()).start();

      // 显示动画节点，隐藏静态节点
      this.drawTubeGif.active = true;
      // 假设还有静态图片节点需要隐藏

      // 2秒后停止动画
      setTimeout(() => {
        cc.Tween.stopAllByTarget(this.drawTubeGif);
        this.drawTubeGif.angle = initialAngle; // 恢复初始角度
        resolve();
      }, (leftTime + rightTime + centerTime) * 1000 * 2);
    });
  }
  showWin() {
    // 隐藏其他元素
    this.drawTubeGif.active = false;
    this.fail.active = false;

    // 显示win节点并播放入场动画
    this.win.active = true;

    // 设置初始状态：缩放为0，透明度为0
    this.win.scale = 0;
    this.win.opacity = 0;

    // 播放弹性入场动画
    cc.tween(this.win)
      .to(0.3, { scale: 1.1, opacity: 255 }, { easing: 'backOut' })
      .to(0.15, { scale: 1.0 }, { easing: 'sineInOut' })
      .start();
  }
  updatePrizeText(text: string): void {
    // 更新数字内容
    this.prizeText.string = text;
    // setTimeout(() => {
    //   const amountWidth = this.goldNumAmount.node.width;
    //   const currentX = this.goldNumText.node.x; // 获取当前X位置
    //   this.goldNumAmount.node.x = currentX - amountWidth / 2 - 10;
    // }, 0); // 0毫秒的延迟
  }
  protected onEnable(): void {
    this.node.scale = 0.5;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(
        cc.tween().to(this.duration, { scale: this.customScale, opacity: 255 })
      )
      .start();

    cc.tween(this.acceptBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();
  }

  // update (dt) {}
}
