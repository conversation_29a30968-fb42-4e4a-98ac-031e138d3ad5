import { DeviceDetector } from './DeviceDetector';

/**
 * 平板适配器
 */
export class TabletAdapter {
  /**
   * 平板适配主逻辑
   */
  static adaptForTablet(
    node: cc.Node,
    options: {
      maxScale?: number;
      centerHorizontally?: boolean;
      addPadding?: boolean;
    } = {}
  ) {
    if (!DeviceDetector.isTablet()) return;

    const {
      maxScale = 1.2,
      centerHorizontally = true,
      addPadding = true,
    } = options;
    const { width, height } = cc.view.getVisibleSize();
    const designResolution = cc.Canvas.instance.designResolution;

    // 计算适配比例，限制最大缩放
    const scaleX = width / designResolution.width;
    const scaleY = height / designResolution.height;
    const scale = Math.min(Math.min(scaleX, scaleY), maxScale);
    cc.log(`scale: ${scale}`);
    node.scale = scale;

    // 水平居中
    if (centerHorizontally) {
      const scaledWidth = designResolution.width * scale;
      const offsetX = (width - scaledWidth) / 2;
      node.x = offsetX;
    }

    // 添加内边距效果
    if (addPadding) {
      const padding = Math.min(width, height) * 0.05; // 5%内边距
      node.scale = scale * (1 - padding / Math.min(width, height));
    }
  }
}
