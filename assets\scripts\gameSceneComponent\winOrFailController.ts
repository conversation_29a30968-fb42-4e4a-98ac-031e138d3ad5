// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
import { isInWeixin, $_share } from '../utils/generalUtil';

@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  message: cc.Node = null;
  // LIFE-CYCLE CALLBACKS:
  @property(cc.Node)
  handleBtn: cc.Node = null;

  @property(cc.Node)
  againBtn: cc.Node = null;

  @property(cc.Node)
  shareBtn: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  videoPop: cc.Node = null;

  duration: number = 0.8;

  onLoad() {
    this.node.scale = 0.5;
    this.node.opacity = 100;
    // 解决控制台报警告 `cc.Node.rotation` is deprecated since v2.1.0, please set `-angle` instead. (`this.node.rotation = x` -> `this.node.angle = -x`)
    // this.message.rotation = 96;
    this.message.angle = 90;
  }

  handleBtnClick() {
    // 如果通关了，则显示抽奖按钮
    let shouldFoundElement =
      globalVariables.LevelShouldFoundElementArray[
        globalVariables.currentLevel - 1
      ];
    if (
      globalVariables.passLevelArray[3] == 1 &&
      globalVariables.passTimeArray[3] != -1 &&
      globalVariables.passTimeArray[3] != 0 &&
      globalVariables.currentFoundElement == shouldFoundElement
    ) {
      // 调用抽奖接口
      // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
      // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
      // 暂时不用抽奖接口，因为没啥奖品，由前端维护随机展示抽中的幸运签
      // Request.getLottery()
      //   .then((res: any) => {
      //     if (res?.prizeType) {
      //       // 修改 maskLottery 的 lotteryInfo 属性
      //       let maskLottery =
      //         this.maskLottery.getComponent('lotteryController');
      //       maskLottery.lotteryInfo = new cc.JsonAsset();
      //       maskLottery.lotteryInfo.json = res;
      //     }
      //     // 显示抽奖结果
      //     this.node.active = false;
      //     // this.maskLottery.active = true;

      //     cc.director.loadScene(AppConstants.DRAW_LOTS);
      //   })
      //   .catch(err => {
      //     console.log(err);
      //   });
      globalVariables.hasShook = false;
      globalVariables.fortuneSticksNum = null;
      cc.director.loadScene(AppConstants.DRAW_LOTS);
      return;
    }
    this.node.active = false;
    this.backToLevelSelect();
  }

  nextBtnClick() {
    this.backToLevelSelect();
  }

  againBtnClick() {
    // 重新开始
    this.backToLevelSelect();
  }

  shareBtnClick() {
    // 调用分享
    this.node.active = false;
    if (isInWeixin) {
      this.node.parent.getChildByName('shareTip').active = true;
    } else {
      $_share(globalVariables.shareInfo);
    }
  }

  videoBtnClick() {
    // 调用分享
    this.node.active = false;
    this.videoPop.active = true;
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  protected onEnable(): void {
    cc.tween(this.node)
      .then(ActionUtils.restoreScaleAndOpacityAction(this.duration))
      .start();

    // let action1 = cc.tween().by(this.duration,{rotation: -6})
    // let action2 = cc.tween().by(this.duration,{rotation: 6})
    // btn做呼吸效果
    cc.tween(this.handleBtn)
      .then(ActionUtils.largenAndLessenAction(1.1, 0.9, 0.8))
      .repeatForever()
      .start();
    // againBtn和shareBtn交替做呼吸效果
    cc.tween(this.againBtn)
      .then(ActionUtils.largenAndLessenAction(1.1, 0.9, 0.8))
      .repeatForever()
      .start();

    cc.tween(this.shareBtn)
      .delay(0.8)
      .call(() => {
        cc.tween(this.shareBtn)
          .then(ActionUtils.largenAndLessenAction(1.1, 0.9, 0.8))
          .repeatForever()
          .start();
      })
      .start();
    let action1 = cc.tween().by(this.duration, { angle: 6 });
    let action2 = cc.tween().by(this.duration, { angle: -6 });

    cc.tween(this.message)
      .repeatForever(cc.tween().sequence(action1, action2))
      .start();
  }

  start() {}

  // update (dt) {}
}
