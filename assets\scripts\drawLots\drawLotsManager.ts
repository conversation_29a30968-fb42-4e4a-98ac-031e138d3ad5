// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  drawTube: cc.Node = null;

  @property(cc.Node)
  drawTubeGif: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  lose: cc.Node = null;

  @property(cc.Label)
  goldNumText: cc.Label = null;

  @property(cc.Node)
  lotteryBtn: cc.Node = null;

  @property(cc.Node)
  againBtn: cc.Node = null;

  @property(cc.Node)
  acceptBtn: cc.Node = null;

  @property(cc.Node)
  confirmBtn: cc.Node = null;

  @property(cc.JsonAsset)
  lotteryInfo: any = null; // 抽奖信息

  duration: number = 0.8;

  // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
  // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
  onLoad() {
    this.node.scale = 0.7;
    this.node.opacity = 100;
    if (this.lotteryInfo?.json?.prizeType) {
      this.showAward();
      this.goldNumText.string = this.lotteryInfo.json.msg.toString();
      this.showGold(); // 暂不区分金币和优惠券，统一用金币的样式展示
    } else {
      this.showFail();
    }
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  playDrawTubeShakeAnimation(): Promise<void> {
    return new Promise(resolve => {
      // 设置签筒锚点为底部中间 (0.5, 0)
      // this.drawTubeGif.anchorX = 0.5;
      // this.drawTubeGif.anchorY = 0;

      // 记录初始角度（已旋转180度）
      const initialAngle = this.drawTubeGif.angle;

      // 左右摇摆动画，基于初始角度进行摇摆
      let leftSwing = cc
        .tween()
        .to(0.2, { angle: initialAngle - 15 }, { easing: 'sineInOut' });
      let rightSwing = cc
        .tween()
        .to(0.4, { angle: initialAngle + 15 }, { easing: 'sineInOut' });
      let centerSwing = cc
        .tween()
        .to(0.2, { angle: initialAngle }, { easing: 'sineInOut' });
      cc.tween(this.drawTubeGif)
        .then(cc.tween().sequence(leftSwing, rightSwing, centerSwing))
        .repeatForever()
        .start();

      // 显示动画节点，隐藏静态节点
      this.drawTubeGif.active = true;
      // 假设还有静态图片节点需要隐藏

      // 2秒后停止动画
      setTimeout(() => {
        cc.Tween.stopAllByTarget(this.drawTubeGif);
        this.drawTubeGif.angle = initialAngle; // 恢复初始角度
        resolve();
      }, 2000);
    });
  }

  acceptBtnClick() {
    this.drawTube.active = true;
    this.maskLottery.active = false;
  }

  showFail() {
    this.maskLottery.active = true;
  }

  showAward() {
    this.maskLottery.active = true;
    this.drawTubeGif.active = true;

    // 记录初始角度（已旋转180度）
    const initialAngle = this.drawTubeGif.angle;

    // 左右摇摆动画，基于初始角度进行摇摆
    cc.tween(this.drawTubeGif)
      .repeatForever(
        cc
          .tween()
          .to(0.2, { angle: initialAngle - 15 }, { easing: 'sineInOut' })
          .to(0.4, { angle: initialAngle + 15 }, { easing: 'sineInOut' })
          .to(0.2, { angle: initialAngle }, { easing: 'sineInOut' })
      )
      .start();
    // this.playDrawTubeShakeAnimation().then(() => {
    //   // 动画完成后显示中奖结果
    //   // this.drawTube.active = false;
    //   this.maskLottery.active = true;
    // });
  }

  showCoupon() {
    // 显示coupon，隐藏gold
    // this.coupon.active = true;
    // this.gold.active = false;
  }

  showGold() {
    // 显示gold，隐藏coupon
    // this.coupon.active = false;
    // this.gold.active = true;
  }
  updateGold(amount) {
    // 更新数字内容
    // this.goldNumAmount.string = amount.toString();
    // setTimeout(() => {
    //   const amountWidth = this.goldNumAmount.node.width;
    //   const currentX = this.goldNumText.node.x; // 获取当前X位置
    //   this.goldNumAmount.node.x = currentX - amountWidth / 2 - 10;
    // }, 0); // 0毫秒的延迟
  }
  protected onEnable(): void {
    this.node.scale = 0.5;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(ActionUtils.restoreScaleAndOpacityAction(this.duration))
      .start();

    // btn做呼吸效果
    cc.tween(this.lotteryBtn)
      .then(ActionUtils.largenAndLessenAction(1.1, 0.9, 0.8))
      .repeatForever()
      .start();

    // againBtn和shareBtn交替做呼吸效果
    cc.tween(this.againBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();

    cc.tween(this.acceptBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();
  }

  start() {}

  // update (dt) {}
}
