// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  lose: cc.Node = null;

  duration: number = 0.8;

  protected onLoad(): void {
    // 启用Retina支持
    cc.view.enableRetina(true);
  }
  onEnable() {
    // 根据globalVariables.fortuneSticksNum的值决定使用保存的签还是随机生成
    let num: number;
    if (globalVariables.fortuneSticksNum !== null) {
      // 如果有保存的幸运签，使用保存的值
      num = globalVariables.fortuneSticksNum;
    } else {
      // 如果没有保存的幸运签，随机生成一个并保存
      num = Math.floor(Math.random() * 6) + 1;
      globalVariables.fortuneSticksNum = num;
    }

    cc.resources.load(
      `drawLots/luckyDraw${num}`,
      cc.SpriteFrame,
      (err, spriteFrame) => {
        if (err) {
          cc.error('加载图片失败: ' + err);
          return;
        }
        // 使用 spriteFrame
        const node = this.lose;
        const frame = spriteFrame as cc.SpriteFrame;
        node.getComponent(cc.Sprite).spriteFrame = frame;
        // node.width = frame.getRect().width;
        // node.height = frame.getRect().height;

        // 应用高清渲染
        const texture = frame.getTexture();
        texture.setFilters(
          cc.Texture2D.Filter.NEAREST,
          cc.Texture2D.Filter.NEAREST
        );
        this.lose.active = true;
        // 在控制台检查加载的纹理
        const tex = this.lose.getComponent(cc.Sprite).spriteFrame.getTexture();
        cc.log(`实际纹理尺寸: ${tex.width} x ${tex.height}`);
        cc.log(`显示尺寸: ${this.lose.width} x ${this.lose.height}`);
      }
    );
  }

  start() {}

  // update (dt) {}
}
