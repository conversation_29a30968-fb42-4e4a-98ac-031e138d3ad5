# 关于安全宣传的微信小游戏

---

## 简介

“慧眼寻安”找茬挑战小游戏, 整体游戏玩法为图片找茬类游戏

## 技术框架

游戏引擎采用： cocos creator（2.4.13）

## 启动项目

web端： 下载cocos creator，选择本项目，打开即可模拟运行

## 项目实现的功能、模块

主要游戏玩法（3个找茬关卡），排名模块

## 游戏文件目录结构

项目asset资源

```txt
    + assets/
        + clip/     动画资源
        + resources/        加载资源
            + CheckInPage/
            + coreGameSceneMaterial/
            + levelSelection/
            + others/
            + rankScene/
        + scene/        场景
        + scripts/      脚本资源（主场景脚本和组件脚本）
            + checkInSceneComponent/        签到页组件脚本
            + gameSceneComponent/       找茬游戏场景脚本
            + globalComponet/       全局组件脚本
            + levelSelectionComponent/      关卡选择组件脚本
            + LuckyDrawSceneComponent/      抽奖页组件脚本
            + mainScenecComponent/      主页组件脚本
            + rankSceneComponet/        排名组件脚本
            + utils/    工具类脚本
            + WXCloudProxy/     微信云服务代理类
        + texture/      立即加载的场景资源
            + bgImg/
            + buttons/
            + headImgs/
            + levelBG/
            + levelSelection/
            + loading/
            + LuckyDraw/
            + messageBar/
            + MP3/
            + others/
            + Prefab/
```

## 游戏预览

![img](./markdownimg/markdownimage01.png)
